#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask服务端 - 单点登录系统
基于激活码和机器特征码实现单点登录机制
"""

from flask import Flask, request, jsonify, session
import uuid
import time
from datetime import datetime, date
import pymysql

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-in-production'  # 生产环境请更换

# ==================== 全局配置 ====================
CONFIG = {
    'MYSQL': {
        'HOST': '127.0.0.1',
        'PORT': 3306,
        'DATABASE': 'myj',
        'TABLE': 'auth',
        'USER': 'admin',  # 根据实际情况修改
        'PASSWORD': 'Xyy1024.',  # 根据实际情况修改
        'CHARSET': 'utf8mb4'
    },
    'SESSION': {
        'TIMEOUT': 3600  # 会话超时时间（秒）
    }
}

# 全局变量存储在线用户信息（内存缓存）
# 格式: {activation_code: {'session_id': str, 'login_time': timestamp, 'last_heartbeat': timestamp, 'device_code': str}}
online_users = {}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(
            host=CONFIG['MYSQL']['HOST'],
            port=CONFIG['MYSQL']['PORT'],
            user=CONFIG['MYSQL']['USER'],
            password=CONFIG['MYSQL']['PASSWORD'],
            database=CONFIG['MYSQL']['DATABASE'],
            charset=CONFIG['MYSQL']['CHARSET'],
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def verify_activation_code(activation_code):
    """验证激活码是否有效"""
    try:
        conn = get_db_connection()
        if not conn:
            return False, "数据库连接失败"

        with conn.cursor() as cursor:
            sql = f"SELECT * FROM {CONFIG['MYSQL']['TABLE']} WHERE 激活码 = %s"
            cursor.execute(sql, (activation_code,))
            result = cursor.fetchone()

            if not result:
                return False, "激活码不存在"

            # 检查有效期
            if result['有效期'] and result['有效期'] < date.today():
                return False, "激活码已过期"

            return True, result
    except Exception as e:
        print(f"验证激活码错误: {e}")
        return False, f"验证失败: {str(e)}"
    finally:
        if conn:
            conn.close()

def update_device_code(activation_code, device_code):
    """更新设备特征码"""
    try:
        conn = get_db_connection()
        if not conn:
            return False

        with conn.cursor() as cursor:
            # 如果device_code为None，则设置为NULL
            if device_code is None:
                sql = f"UPDATE {CONFIG['MYSQL']['TABLE']} SET 特征码 = NULL WHERE 激活码 = %s"
                cursor.execute(sql, (activation_code,))
            else:
                sql = f"UPDATE {CONFIG['MYSQL']['TABLE']} SET 特征码 = %s WHERE 激活码 = %s"
                cursor.execute(sql, (device_code, activation_code))
            conn.commit()
            return True
    except Exception as e:
        print(f"更新特征码错误: {e}")
        return False
    finally:
        if conn:
            conn.close()

@app.route('/login', methods=['POST'])
def login():
    """
    激活码登录接口
    POST /login
    Body: {"activation_code": "激活码", "device_code": "设备特征码"}
    """
    try:
        data = request.get_json()
        if not data or 'activation_code' not in data:
            return jsonify({'success': False, 'message': '缺少激活码'}), 400

        activation_code = data['activation_code']
        device_code = data.get('device_code', '')

        # 验证激活码
        is_valid, result = verify_activation_code(activation_code)
        if not is_valid:
            return jsonify({'success': False, 'message': result}), 400

        # 检查设备特征码
        current_device_code = result.get('特征码')

        if current_device_code is None or current_device_code == '':
            # 特征码为空，设置为当前设备特征码
            if not update_device_code(activation_code, device_code):
                return jsonify({'success': False, 'message': '更新设备特征码失败'}), 500
            print(f"[{datetime.now()}] 激活码 {activation_code} 首次登录，设置特征码: {device_code}")
        elif current_device_code != device_code:
            # 特征码不一致，踢掉旧设备
            if activation_code in online_users:
                old_session = online_users[activation_code]['session_id']
                print(f"[{datetime.now()}] 激活码 {activation_code} 在新设备登录，踢掉旧会话: {old_session}")

            # 更新为新的设备特征码
            if not update_device_code(activation_code, device_code):
                return jsonify({'success': False, 'message': '更新设备特征码失败'}), 500
            print(f"[{datetime.now()}] 激活码 {activation_code} 切换设备，更新特征码: {device_code}")

        # 生成新的会话ID
        new_session_id = str(uuid.uuid4())
        current_time = time.time()

        # 更新在线用户表（自动踢掉旧会话）
        online_users[activation_code] = {
            'session_id': new_session_id,
            'login_time': current_time,
            'last_heartbeat': current_time,
            'device_code': device_code
        }

        # 设置Flask session
        session['activation_code'] = activation_code
        session['session_id'] = new_session_id
        session['device_code'] = device_code

        print(f"[{datetime.now()}] 激活码 {activation_code} 登录成功，会话ID: {new_session_id}")

        return jsonify({
            'success': True,
            'message': '登录成功',
            'session_id': new_session_id,
            'activation_code': activation_code
        })
        
    except Exception as e:
        print(f"登录错误: {e}")
        return jsonify({'success': False, 'message': '登录失败'}), 500

@app.route('/logout', methods=['POST'])
def logout():
    """
    用户注销接口
    POST /logout
    """
    try:
        activation_code = session.get('activation_code')
        session_id = session.get('session_id')

        if activation_code and activation_code in online_users:
            # 验证会话ID是否匹配
            if online_users[activation_code]['session_id'] == session_id:
                # 从在线用户列表中移除
                del online_users[activation_code]

                # 将数据库中的特征码设置为NULL
                if update_device_code(activation_code, None):
                    print(f"[{datetime.now()}] 激活码 {activation_code} 主动注销，特征码已清空")
                else:
                    print(f"[{datetime.now()}] 激活码 {activation_code} 主动注销，但清空特征码失败")
            else:
                print(f"[{datetime.now()}] 激活码 {activation_code} 会话ID不匹配，可能已被踢下线")

        # 清除Flask session
        session.clear()

        return jsonify({'success': True, 'message': '注销成功'})

    except Exception as e:
        print(f"注销错误: {e}")
        return jsonify({'success': False, 'message': '注销失败'}), 500

@app.route('/status', methods=['GET'])
def check_status():
    """
    检查用户在线状态
    GET /status
    """
    try:
        activation_code = session.get('activation_code')
        session_id = session.get('session_id')
        device_code = session.get('device_code')

        if not activation_code or not session_id:
            return jsonify({'online': False, 'message': '未登录'})

        # 检查用户是否在在线列表中
        if activation_code not in online_users:
            session.clear()
            return jsonify({'online': False, 'message': '会话已过期'})

        # 检查会话ID是否匹配
        if online_users[activation_code]['session_id'] != session_id:
            session.clear()
            return jsonify({'online': False, 'message': '账号已在其他设备登录'})

        # 检查设备特征码是否匹配
        if online_users[activation_code]['device_code'] != device_code:
            session.clear()
            return jsonify({'online': False, 'message': '设备特征码不匹配'})

        # 更新心跳时间
        online_users[activation_code]['last_heartbeat'] = time.time()

        return jsonify({
            'online': True,
            'activation_code': activation_code,
            'session_id': session_id,
            'device_code': device_code,
            'login_time': online_users[activation_code]['login_time']
        })

    except Exception as e:
        print(f"状态检查错误: {e}")
        return jsonify({'online': False, 'message': '状态检查失败'}), 500

@app.route('/heartbeat', methods=['POST'])
def heartbeat():
    """
    心跳接口，保持连接活跃
    POST /heartbeat
    """
    try:
        activation_code = session.get('activation_code')
        session_id = session.get('session_id')
        device_code = session.get('device_code')

        if not activation_code or not session_id:
            return jsonify({'success': False, 'message': '未登录'}), 401

        if (activation_code not in online_users or
            online_users[activation_code]['session_id'] != session_id or
            online_users[activation_code]['device_code'] != device_code):
            return jsonify({'success': False, 'message': '会话无效'}), 401

        # 更新心跳时间
        online_users[activation_code]['last_heartbeat'] = time.time()

        return jsonify({'success': True, 'message': '心跳正常'})

    except Exception as e:
        print(f"心跳错误: {e}")
        return jsonify({'success': False, 'message': '心跳失败'}), 500

@app.route('/online_users', methods=['GET'])
def get_online_users():
    """
    获取当前在线用户列表（管理接口）
    GET /online_users
    """
    try:
        current_time = time.time()
        users_info = []

        for activation_code, info in online_users.items():
            users_info.append({
                'activation_code': activation_code[:8] + '...',  # 只显示部分激活码
                'session_id': info['session_id'][:8] + '...',  # 只显示部分session_id
                'device_code': info['device_code'][:8] + '...' if info['device_code'] else 'N/A',  # 只显示部分设备码
                'login_time': datetime.fromtimestamp(info['login_time']).strftime('%Y-%m-%d %H:%M:%S'),
                'last_heartbeat': datetime.fromtimestamp(info['last_heartbeat']).strftime('%Y-%m-%d %H:%M:%S'),
                'online_duration': int(current_time - info['login_time'])
            })

        return jsonify({
            'success': True,
            'online_count': len(online_users),
            'users': users_info
        })

    except Exception as e:
        print(f"获取在线用户错误: {e}")
        return jsonify({'success': False, 'message': '获取失败'}), 500

if __name__ == '__main__':
    print("=" * 60)
    print("Flask单点登录服务端启动 - 基于激活码和设备特征码")
    print("=" * 60)
    print("数据库配置:")
    print(f"  MySQL地址: {CONFIG['MYSQL']['HOST']}:{CONFIG['MYSQL']['PORT']}")
    print(f"  数据库名: {CONFIG['MYSQL']['DATABASE']}")
    print(f"  数据表名: {CONFIG['MYSQL']['TABLE']}")
    print("-" * 60)
    print("服务地址: http://127.0.0.1:5000")
    print("API接口:")
    print("  POST /login      - 激活码登录 (需要: activation_code, device_code)")
    print("  POST /logout     - 用户注销")
    print("  GET  /status     - 检查状态")
    print("  POST /heartbeat  - 发送心跳")
    print("  GET  /online_users - 查看在线用户")
    print("=" * 60)

    # 启动Flask服务
    app.run(host='127.0.0.1', port=5000, debug=True)
