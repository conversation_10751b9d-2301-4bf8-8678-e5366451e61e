#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask服务端 - 单点登录系统
实现类似QQ的在线/离线机制，同一账号最多只能在一个设备上登录
"""

from flask import Flask, request, jsonify, session
import uuid
import time
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-in-production'  # 生产环境请更换

# 全局变量存储在线用户信息
# 格式: {username: {'session_id': str, 'login_time': timestamp, 'last_heartbeat': timestamp}}
online_users = {}

@app.route('/login', methods=['POST'])
def login():
    """
    用户登录接口
    POST /login
    Body: {"username": "用户名", "password": "密码"}
    """
    try:
        data = request.get_json()
        if not data or 'username' not in data:
            return jsonify({'success': False, 'message': '缺少用户名'}), 400
        
        username = data['username']
        password = data.get('password', '')  # 简化版本，实际应该验证密码
        
        # 检查是否已有用户在线
        if username in online_users:
            old_session = online_users[username]['session_id']
            print(f"[{datetime.now()}] 用户 {username} 在新设备登录，踢掉旧会话: {old_session}")
        
        # 生成新的会话ID
        new_session_id = str(uuid.uuid4())
        current_time = time.time()
        
        # 更新在线用户表（自动踢掉旧会话）
        online_users[username] = {
            'session_id': new_session_id,
            'login_time': current_time,
            'last_heartbeat': current_time
        }
        
        # 设置Flask session
        session['username'] = username
        session['session_id'] = new_session_id
        
        print(f"[{datetime.now()}] 用户 {username} 登录成功，会话ID: {new_session_id}")
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'session_id': new_session_id,
            'username': username
        })
        
    except Exception as e:
        print(f"登录错误: {e}")
        return jsonify({'success': False, 'message': '登录失败'}), 500

@app.route('/logout', methods=['POST'])
def logout():
    """
    用户注销接口
    POST /logout
    """
    try:
        username = session.get('username')
        session_id = session.get('session_id')
        
        if username and username in online_users:
            # 验证会话ID是否匹配
            if online_users[username]['session_id'] == session_id:
                del online_users[username]
                print(f"[{datetime.now()}] 用户 {username} 主动注销")
            else:
                print(f"[{datetime.now()}] 用户 {username} 会话ID不匹配，可能已被踢下线")
        
        # 清除Flask session
        session.clear()
        
        return jsonify({'success': True, 'message': '注销成功'})
        
    except Exception as e:
        print(f"注销错误: {e}")
        return jsonify({'success': False, 'message': '注销失败'}), 500

@app.route('/status', methods=['GET'])
def check_status():
    """
    检查用户在线状态
    GET /status
    """
    try:
        username = session.get('username')
        session_id = session.get('session_id')
        
        if not username or not session_id:
            return jsonify({'online': False, 'message': '未登录'})
        
        # 检查用户是否在在线列表中
        if username not in online_users:
            session.clear()
            return jsonify({'online': False, 'message': '会话已过期'})
        
        # 检查会话ID是否匹配
        if online_users[username]['session_id'] != session_id:
            session.clear()
            return jsonify({'online': False, 'message': '账号已在其他设备登录'})
        
        # 更新心跳时间
        online_users[username]['last_heartbeat'] = time.time()
        
        return jsonify({
            'online': True,
            'username': username,
            'session_id': session_id,
            'login_time': online_users[username]['login_time']
        })
        
    except Exception as e:
        print(f"状态检查错误: {e}")
        return jsonify({'online': False, 'message': '状态检查失败'}), 500

@app.route('/heartbeat', methods=['POST'])
def heartbeat():
    """
    心跳接口，保持连接活跃
    POST /heartbeat
    """
    try:
        username = session.get('username')
        session_id = session.get('session_id')
        
        if not username or not session_id:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        if username not in online_users or online_users[username]['session_id'] != session_id:
            return jsonify({'success': False, 'message': '会话无效'}), 401
        
        # 更新心跳时间
        online_users[username]['last_heartbeat'] = time.time()
        
        return jsonify({'success': True, 'message': '心跳正常'})
        
    except Exception as e:
        print(f"心跳错误: {e}")
        return jsonify({'success': False, 'message': '心跳失败'}), 500

@app.route('/online_users', methods=['GET'])
def get_online_users():
    """
    获取当前在线用户列表（管理接口）
    GET /online_users
    """
    try:
        current_time = time.time()
        users_info = []
        
        for username, info in online_users.items():
            users_info.append({
                'username': username,
                'session_id': info['session_id'][:8] + '...',  # 只显示部分session_id
                'login_time': datetime.fromtimestamp(info['login_time']).strftime('%Y-%m-%d %H:%M:%S'),
                'last_heartbeat': datetime.fromtimestamp(info['last_heartbeat']).strftime('%Y-%m-%d %H:%M:%S'),
                'online_duration': int(current_time - info['login_time'])
            })
        
        return jsonify({
            'success': True,
            'online_count': len(online_users),
            'users': users_info
        })
        
    except Exception as e:
        print(f"获取在线用户错误: {e}")
        return jsonify({'success': False, 'message': '获取失败'}), 500

if __name__ == '__main__':
    print("=" * 50)
    print("Flask单点登录服务端启动")
    print("服务地址: http://127.0.0.1:5000")
    print("API接口:")
    print("  POST /login      - 用户登录")
    print("  POST /logout     - 用户注销")
    print("  GET  /status     - 检查状态")
    print("  POST /heartbeat  - 发送心跳")
    print("  GET  /online_users - 查看在线用户")
    print("=" * 50)
    
    # 启动Flask服务
    app.run(host='127.0.0.1', port=5000, debug=True)
