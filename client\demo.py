#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端 - 单点登录系统GUI客户端
使用tkinter图形界面连接Flask服务端
"""

import requests
import time
import json
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
import threading

class LoginClient:
    def __init__(self, server_url='http://127.0.0.1:5000'):
        self.server_url = server_url
        self.session = requests.Session()  # 使用Session保持cookie
        self.username = None
        self.session_id = None
        self.is_online = False
        self.heartbeat_thread = None
        self.heartbeat_running = False

    def login(self, username, password='', callback=None):
        """
        登录到服务端
        """
        try:
            url = f"{self.server_url}/login"
            data = {
                'username': username,
                'password': password
            }

            response = self.session.post(url, json=data)
            result = response.json()

            if result.get('success'):
                self.username = username
                self.session_id = result.get('session_id')
                self.is_online = True
                if callback:
                    callback('success', f"登录成功! 用户: {self.username}")
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"登录失败: {message}")
                return False

        except requests.exceptions.ConnectionError:
            if callback:
                callback('error', "无法连接到服务端，请确保服务端已启动")
            return False
        except Exception as e:
            if callback:
                callback('error', f"登录异常: {str(e)}")
            return False

    def logout(self, callback=None):
        """
        从服务端注销
        """
        try:
            url = f"{self.server_url}/logout"

            response = self.session.post(url)
            result = response.json()

            if result.get('success'):
                self.username = None
                self.session_id = None
                self.is_online = False
                self.heartbeat_running = False
                if callback:
                    callback('success', "注销成功")
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"注销失败: {message}")
                return False

        except Exception as e:
            if callback:
                callback('error', f"注销异常: {str(e)}")
            return False

    def check_status(self, callback=None):
        """
        检查在线状态
        """
        try:
            url = f"{self.server_url}/status"

            response = self.session.get(url)
            result = response.json()

            if result.get('online'):
                self.is_online = True
                if callback:
                    callback('online', "在线状态正常")
                return True
            else:
                self.is_online = False
                self.heartbeat_running = False
                message = result.get('message', '未知原因')
                if '其他设备' in message:
                    if callback:
                        callback('kicked', "账号已在其他设备登录，被强制下线")
                else:
                    if callback:
                        callback('offline', f"离线状态: {message}")
                return False

        except Exception as e:
            self.is_online = False
            if callback:
                callback('error', f"状态检查异常: {str(e)}")
            return False

    def send_heartbeat(self, callback=None):
        """
        发送心跳保持连接
        """
        try:
            url = f"{self.server_url}/heartbeat"

            response = self.session.post(url)
            result = response.json()

            if result.get('success'):
                if callback:
                    callback('success', "心跳正常")
                return True
            else:
                self.is_online = False
                self.heartbeat_running = False
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"心跳失败: {message}")
                return False

        except Exception as e:
            self.is_online = False
            if callback:
                callback('error', f"心跳异常: {str(e)}")
            return False

    def get_online_users(self, callback=None):
        """
        获取在线用户列表
        """
        try:
            url = f"{self.server_url}/online_users"

            response = self.session.get(url)
            result = response.json()

            if result.get('success'):
                users_info = []
                for user in result.get('users', []):
                    users_info.append(f"👤 {user['username']} | 登录: {user['login_time']} | 在线: {user['online_duration']}秒")

                if callback:
                    callback('success', {
                        'count': result.get('online_count', 0),
                        'users': users_info
                    })
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"获取在线用户失败: {message}")
                return False

        except Exception as e:
            if callback:
                callback('error', f"获取在线用户异常: {str(e)}")
            return False

    def start_heartbeat(self, callback=None):
        """
        启动心跳线程
        """
        if not self.heartbeat_running and self.is_online:
            self.heartbeat_running = True
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_worker, args=(callback,))
            self.heartbeat_thread.daemon = True
            self.heartbeat_thread.start()

    def stop_heartbeat(self):
        """
        停止心跳线程
        """
        self.heartbeat_running = False

    def _heartbeat_worker(self, callback):
        """
        心跳工作线程
        """
        while self.heartbeat_running and self.is_online:
            time.sleep(5)  # 每5秒检查一次
            if self.heartbeat_running:
                self.check_status(callback)

class LoginGUI:
    def __init__(self):
        self.client = LoginClient()
        self.root = tk.Tk()
        self.root.title("🔐 单点登录系统")
        self.root.geometry("500x600")
        self.root.resizable(False, False)

        # 设置窗口居中
        self.center_window()

        # 创建界面
        self.create_widgets()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="🔐 单点登录系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 登录区域
        login_frame = ttk.LabelFrame(main_frame, text="登录信息", padding="10")
        login_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(login_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(login_frame, textvariable=self.username_var, width=30)
        self.username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 登录按钮
        self.login_btn = ttk.Button(login_frame, text="登录", command=self.login)
        self.login_btn.grid(row=1, column=0, columnspan=2, pady=10)

        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.status_var = tk.StringVar(value="未连接")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, font=("Arial", 12))
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # 操作按钮区域
        button_frame = ttk.LabelFrame(main_frame, text="操作", padding="10")
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.heartbeat_btn = ttk.Button(button_frame, text="开启心跳", command=self.toggle_heartbeat, state="disabled")
        self.heartbeat_btn.grid(row=0, column=0, padx=5, pady=5)

        self.status_btn = ttk.Button(button_frame, text="检查状态", command=self.check_status, state="disabled")
        self.status_btn.grid(row=0, column=1, padx=5, pady=5)

        self.users_btn = ttk.Button(button_frame, text="在线用户", command=self.show_online_users, state="disabled")
        self.users_btn.grid(row=0, column=2, padx=5, pady=5)

        self.logout_btn = ttk.Button(button_frame, text="注销", command=self.logout, state="disabled")
        self.logout_btn.grid(row=1, column=0, columnspan=3, pady=10)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="系统日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=60)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        login_frame.columnconfigure(1, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 初始化日志
        self.add_log("系统启动完成，请输入用户名登录")

        # 绑定回车键登录
        self.username_entry.bind('<Return>', lambda e: self.login())
    def add_log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def login(self):
        """登录操作"""
        username = self.username_var.get().strip()
        if not username:
            messagebox.showerror("错误", "请输入用户名")
            return

        self.login_btn.config(state="disabled", text="登录中...")
        self.add_log(f"尝试登录用户: {username}")

        # 在新线程中执行登录
        threading.Thread(target=self._login_worker, args=(username,), daemon=True).start()

    def _login_worker(self, username):
        """登录工作线程"""
        def callback(status, message):
            self.root.after(0, self._handle_login_result, status, message)

        self.client.login(username, callback=callback)

    def _handle_login_result(self, status, message):
        """处理登录结果"""
        self.add_log(message)

        if status == 'success':
            self.status_var.set(f"✅ 已登录: {self.client.username}")
            self.login_btn.config(state="disabled", text="已登录")
            self.username_entry.config(state="disabled")

            # 启用其他按钮
            self.heartbeat_btn.config(state="normal")
            self.status_btn.config(state="normal")
            self.users_btn.config(state="normal")
            self.logout_btn.config(state="normal")

            # 自动开启心跳
            self.start_heartbeat()

        else:
            self.login_btn.config(state="normal", text="登录")

    def logout(self):
        """注销操作"""
        if not self.client.is_online:
            return

        self.add_log("正在注销...")

        # 在新线程中执行注销
        threading.Thread(target=self._logout_worker, daemon=True).start()

    def _logout_worker(self):
        """注销工作线程"""
        def callback(status, message):
            self.root.after(0, self._handle_logout_result, status, message)

        self.client.logout(callback=callback)

    def _handle_logout_result(self, status, message):
        """处理注销结果"""
        self.add_log(message)
        self.reset_ui()

    def check_status(self):
        """检查状态"""
        if not self.client.is_online:
            return

        # 在新线程中检查状态
        threading.Thread(target=self._check_status_worker, daemon=True).start()

    def _check_status_worker(self):
        """状态检查工作线程"""
        def callback(status, message):
            self.root.after(0, self._handle_status_result, status, message)

        self.client.check_status(callback=callback)

    def _handle_status_result(self, status, message):
        """处理状态检查结果"""
        if status == 'online':
            self.add_log("✅ " + message)
        elif status == 'kicked':
            self.add_log("⚠️ " + message)
            messagebox.showwarning("账号被踢下线", message)
            self.reset_ui()
        else:
            self.add_log("❌ " + message)
            if status == 'offline':
                self.reset_ui()

    def show_online_users(self):
        """显示在线用户"""
        # 在新线程中获取在线用户
        threading.Thread(target=self._get_users_worker, daemon=True).start()

    def _get_users_worker(self):
        """获取在线用户工作线程"""
        def callback(status, data):
            self.root.after(0, self._handle_users_result, status, data)

        self.client.get_online_users(callback=callback)

    def _handle_users_result(self, status, data):
        """处理在线用户结果"""
        if status == 'success':
            count = data['count']
            users = data['users']

            # 创建新窗口显示在线用户
            users_window = tk.Toplevel(self.root)
            users_window.title("在线用户列表")
            users_window.geometry("400x300")
            users_window.resizable(False, False)

            # 居中显示
            users_window.transient(self.root)
            users_window.grab_set()

            frame = ttk.Frame(users_window, padding="10")
            frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(frame, text=f"当前在线用户数: {count}", font=("Arial", 12, "bold")).pack(pady=(0, 10))

            # 用户列表
            users_text = scrolledtext.ScrolledText(frame, height=12, width=50)
            users_text.pack(fill=tk.BOTH, expand=True)

            for user in users:
                users_text.insert(tk.END, user + "\n")

            users_text.config(state="disabled")

            self.add_log(f"📊 获取到 {count} 个在线用户")
        else:
            self.add_log("❌ " + data)

    def toggle_heartbeat(self):
        """切换心跳状态"""
        if self.client.heartbeat_running:
            self.stop_heartbeat()
        else:
            self.start_heartbeat()

    def start_heartbeat(self):
        """开启心跳"""
        if not self.client.is_online:
            return

        def callback(status, message):
            self.root.after(0, self._handle_heartbeat_result, status, message)

        self.client.start_heartbeat(callback=callback)
        self.heartbeat_btn.config(text="停止心跳")
        self.add_log("🔄 心跳监控已开启")

    def stop_heartbeat(self):
        """停止心跳"""
        self.client.stop_heartbeat()
        self.heartbeat_btn.config(text="开启心跳")
        self.add_log("⏹️ 心跳监控已停止")

    def _handle_heartbeat_result(self, status, message):
        """处理心跳结果"""
        if status == 'online':
            # 正常在线，不显示日志避免刷屏
            pass
        elif status == 'kicked':
            self.add_log("⚠️ " + message)
            messagebox.showwarning("账号被踢下线", message)
            self.reset_ui()
        elif status == 'error' or status == 'offline':
            self.add_log("❌ " + message)
            self.reset_ui()

    def reset_ui(self):
        """重置UI状态"""
        self.client.stop_heartbeat()
        self.status_var.set("未连接")
        self.login_btn.config(state="normal", text="登录")
        self.username_entry.config(state="normal")
        self.heartbeat_btn.config(state="disabled", text="开启心跳")
        self.status_btn.config(state="disabled")
        self.users_btn.config(state="disabled")
        self.logout_btn.config(state="disabled")

    def on_closing(self):
        """窗口关闭事件"""
        if self.client.is_online:
            self.client.logout()
        self.client.stop_heartbeat()
        self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主程序入口"""
    app = LoginGUI()
    app.run()

if __name__ == '__main__':
    main()