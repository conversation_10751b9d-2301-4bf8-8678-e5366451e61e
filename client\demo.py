#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端 - 单点登录系统测试客户端
连接Flask服务端进行登录测试
"""

import requests
import time
import json
from datetime import datetime

class LoginClient:
    def __init__(self, server_url='http://127.0.0.1:5000'):
        self.server_url = server_url
        self.session = requests.Session()  # 使用Session保持cookie
        self.username = None
        self.session_id = None
        self.is_online = False
    
    def login(self, username, password=''):
        """
        登录到服务端
        """
        try:
            url = f"{self.server_url}/login"
            data = {
                'username': username,
                'password': password
            }
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 尝试登录用户: {username}")
            
            response = self.session.post(url, json=data)
            result = response.json()
            
            if result.get('success'):
                self.username = username
                self.session_id = result.get('session_id')
                self.is_online = True
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 登录成功!")
                print(f"    用户名: {self.username}")
                print(f"    会话ID: {self.session_id[:8]}...")
                return True
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 登录失败: {result.get('message')}")
                return False
                
        except requests.exceptions.ConnectionError:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 无法连接到服务端，请确保服务端已启动")
            return False
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 登录异常: {e}")
            return False
    
    def logout(self):
        """
        从服务端注销
        """
        try:
            url = f"{self.server_url}/logout"
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 尝试注销...")
            
            response = self.session.post(url)
            result = response.json()
            
            if result.get('success'):
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 注销成功")
                self.username = None
                self.session_id = None
                self.is_online = False
                return True
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 注销失败: {result.get('message')}")
                return False
                
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 注销异常: {e}")
            return False
    
    def check_status(self):
        """
        检查在线状态
        """
        try:
            url = f"{self.server_url}/status"
            
            response = self.session.get(url)
            result = response.json()
            
            if result.get('online'):
                self.is_online = True
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 在线状态正常")
                return True
            else:
                self.is_online = False
                message = result.get('message', '未知原因')
                if '其他设备' in message:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️  账号已在其他设备登录，被强制下线")
                else:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 离线状态: {message}")
                return False
                
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 状态检查异常: {e}")
            self.is_online = False
            return False
    
    def send_heartbeat(self):
        """
        发送心跳保持连接
        """
        try:
            url = f"{self.server_url}/heartbeat"
            
            response = self.session.post(url)
            result = response.json()
            
            if result.get('success'):
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 💓 心跳正常")
                return True
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 心跳失败: {result.get('message')}")
                self.is_online = False
                return False
                
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 心跳异常: {e}")
            return False
    
    def get_online_users(self):
        """
        获取在线用户列表
        """
        try:
            url = f"{self.server_url}/online_users"
            
            response = self.session.get(url)
            result = response.json()
            
            if result.get('success'):
                print(f"\n📊 当前在线用户数: {result.get('online_count', 0)}")
                for user in result.get('users', []):
                    print(f"  👤 {user['username']} | 登录时间: {user['login_time']} | 在线时长: {user['online_duration']}秒")
                print()
                return True
            else:
                print(f"❌ 获取在线用户失败: {result.get('message')}")
                return False
                
        except Exception as e:
            print(f"❌ 获取在线用户异常: {e}")
            return False

def main():
    """
    客户端主程序
    """
    print("=" * 60)
    print("🔐 单点登录系统 - 客户端测试程序")
    print("=" * 60)
    
    client = LoginClient()
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 登录")
        print("2. 检查状态")
        print("3. 发送心跳")
        print("4. 查看在线用户")
        print("5. 注销")
        print("6. 自动心跳模式（每5秒检查一次状态）")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-6): ").strip()
        
        if choice == '1':
            username = input("请输入用户名: ").strip()
            if username:
                client.login(username)
            else:
                print("❌ 用户名不能为空")
        
        elif choice == '2':
            client.check_status()
        
        elif choice == '3':
            if client.is_online:
                client.send_heartbeat()
            else:
                print("❌ 请先登录")
        
        elif choice == '4':
            client.get_online_users()
        
        elif choice == '5':
            if client.is_online:
                client.logout()
            else:
                print("❌ 当前未登录")
        
        elif choice == '6':
            if not client.is_online:
                print("❌ 请先登录")
                continue
            
            print("🔄 进入自动心跳模式，按 Ctrl+C 退出...")
            try:
                while client.is_online:
                    client.check_status()
                    if client.is_online:
                        client.send_heartbeat()
                    time.sleep(5)
                print("⚠️  检测到离线，退出自动心跳模式")
            except KeyboardInterrupt:
                print("\n⏹️  用户中断，退出自动心跳模式")
        
        elif choice == '0':
            if client.is_online:
                print("正在注销...")
                client.logout()
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选项，请重新选择")

if __name__ == '__main__':
    main()
